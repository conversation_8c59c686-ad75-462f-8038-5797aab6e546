import { JulianDate, ShadowMode, defined } from "cesium";

/**
 * Gelişmiş Gölge Analizi Kontrol Sınıfı
 */
export class ShadowAnalysis {
  /**
   * @param {Viewer} viewer Cesium Viewer nesnesi
   */
  constructor(viewer) {
    if (!defined(viewer)) throw new Error("Viewer gerekli.");
    this.viewer = viewer;
    this._currentDate = new Date();
    this._softShadowsEnabled = true;
    this._shadowMapSize = 2048;
    this._onTimeUpdateCallback = null;

    // Varsayılan ayarlar
    this.resetToDefaults();

    // Zaman güncelleme eventi
    this.viewer.clock.onTick.addEventListener(this._handleClockTick.bind(this));
  }

  /**
   * Varsayılan ayarları yükler
   */
  resetToDefaults() {
    this.viewer.scene.globe.enableLighting = true;
    this.viewer.shadows = true;
    this.viewer.scene.shadowMap.enabled = true;
    this.viewer.scene.shadowMap.softShadows = this._softShadowsEnabled;
    this.viewer.scene.shadowMap.size = this._shadowMapSize;
    this.viewer.scene.globe.showGroundAtmosphere = true;
    this.viewer.clock.shouldAnimate = false;
    this.viewer.scene.fog.enabled = true;
  }

  /**
   * Gölgeleri açar/kapatır
   * @param {boolean} enabled
   */
  setShadowsEnabled(enabled) {
    this.viewer.scene.shadowMap.enabled = enabled;
    return this;
  }

  /**
   * Yumuşak gölgeleri açar/kapatır
   * @param {boolean} enabled
   */
  setSoftShadows(enabled) {
    this._softShadowsEnabled = enabled;
    this.viewer.scene.shadowMap.softShadows = enabled;
    return this;
  }

  /**
   * Gölge harita boyutunu ayarlar
   * @param {number} size 256, 512, 1024 veya 2048
   */
  setShadowMapSize(size) {
    const validSizes = [256, 512, 1024, 2048];
    if (!validSizes.includes(size)) {
      console.warn(
        `Geçersiz shadow map size. Kullanılabilir değerler: ${validSizes.join(
          ", "
        )}`
      );
      return this;
    }
    this._shadowMapSize = size;
    this.viewer.scene.shadowMap.size = size;
    return this;
  }

  /**
   * Tarih ve saat ayarlar
   * @param {Date} date
   */
  setDateTime(date) {
    if (!(date instanceof Date)) {
      throw new Error("Geçerli bir Date nesnesi gerekli.");
    }
    this._currentDate = date;
    this.viewer.clock.currentTime = JulianDate.fromDate(date);
    return this;
  }

  /**
   * Sadece tarih ayarlar (saat değişmez)
   * @param {Date} date
   */
  setDate(date) {
    if (!(date instanceof Date)) {
      throw new Error("Geçerli bir Date nesnesi gerekli.");
    }

    // Mevcut saati koruyarak yeni tarihi ayarla
    const newDate = new Date(date);
    newDate.setHours(
      this._currentDate.getHours(),
      this._currentDate.getMinutes(),
      this._currentDate.getSeconds()
    );

    this.setDateTime(newDate);
    return this;
  }

  /**
   * Sadece saat ayarlar (tarih değişmez)
   * @param {number} hours
   * @param {number} minutes
   * @param {number} seconds
   */
  setTime(hours, minutes = 0, seconds = 0) {
    const newDate = new Date(this._currentDate);
    newDate.setHours(hours, minutes, seconds);
    this.setDateTime(newDate);
    return this;
  }

  /**
   * Saati belirtilen aralıkta ayarlar
   * @param {number} value 0-1 arası değer (0: 00:00, 1: 23:59)
   */
  setTimeByRange(value) {
    value = Math.max(0, Math.min(1, value)); // 0-1 arasına clamp
    const hours = Math.floor(value * 23);
    const minutes = Math.floor((value * 23 - hours) * 60);
    this.setTime(hours, minutes);
    return this;
  }

  /**
   * Zaman animasyonunu başlatır
   * @param {number} speedMultiplier Hız çarpanı (varsayılan: 100)
   */
  play(speedMultiplier = 100) {
    this.viewer.clock.shouldAnimate = true;
    this.viewer.clock.multiplier = speedMultiplier;
    return this;
  }

  /**
   * Zaman animasyonunu duraklatır
   */
  pause() {
    this.viewer.clock.shouldAnimate = false;
    return this;
  }

  /**
   * Zaman güncelleme event handler'ı
   */
  _handleClockTick() {
    this._currentDate = JulianDate.toDate(this.viewer.clock.currentTime);
    if (this._onTimeUpdateCallback) {
      this._onTimeUpdateCallback(this._currentDate);
    }
  }

  /**
   * Zaman güncellendiğinde çağrılacak callback'i ayarlar
   * @param {(date: Date) => void} callback
   */
  onTimeUpdate(callback) {
    this._onTimeUpdateCallback = callback;
    return this;
  }

  /**
   * Mevcut tarih ve saati döndürür
   * @returns {Date}
   */
  getCurrentDateTime() {
    return new Date(this._currentDate);
  }

  /**
   * Mevcut ayarları döndürür
   * @returns {object}
   */
  getSettings() {
    return {
      shadowsEnabled: this.viewer.scene.shadowMap.enabled,
      softShadows: this._softShadowsEnabled,
      shadowMapSize: this._shadowMapSize,
      currentDateTime: this.getCurrentDateTime(),
    };
  }
}
