import ReactDOM from "react-dom/client";

import App from "./App";
import {
  addPointToViewer,
  add3DTileToViewer,
  ShadowAnalysis,
  setupClickHandler,
} from "./lib";

// ReactDOM.createRoot(document.getElementById("root")).render(
//   <>
//     <App />
//   </>
// );

function initCesiumMap(containerId, options = {}) {
  const container = document.getElementById(containerId);
  if (!container) throw new Error("Container bulunamadı: " + containerId);

  return new Promise((resolve, reject) => {
    try {
      ReactDOM.createRoot(container).render(
        <App resolve={resolve} options={null} />
      );
    } catch (err) {
      reject(err);
    }
  });
}

const NCCesiumAPI = {
  initCesiumMap,
  addPointToViewer,
  add3DTileToViewer,
  ShadowAnalysis,
  setupClickHandler,
};

export default NCCesiumAPI;
