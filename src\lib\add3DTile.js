import {
  Cesium3DTileset,
  IonResource,
  Ion,
  Cesium3DTileStyle,
  Matrix4,
  defined,
} from "cesium";

/**
 * 3D Tiles dosyasını Cesium Viewer'a ekler
 * @param {Viewer} viewer - Cesium Viewer nesnesi
 * @param {string|number} source - 3D Tiles URL'si veya Ion Asset ID
 * @param {Object} [options] - Opsiyonel ayarlar (örneğin konumlandırma)
 * @returns {Promise<Cesium3DTileset>} Eklenen tileset nesnesi
 */
export async function add3DTileToViewer(viewer, source, options = {}) {
  try {
    let tileset;

    if (typeof source === "number") {
      // Ion Asset ID kullanımı
      tileset = await Cesium3DTileset.fromIonAssetId(source);

      // Ion stilini uygula (varsa)
      const extras = tileset.asset.extras;
      if (
        defined(extras) &&
        defined(extras.ion) &&
        defined(extras.ion.defaultStyle)
      ) {
        tileset.style = new Cesium3DTileStyle(extras.ion.defaultStyle);
      }
    } else {
      // URL üzerinden yükleme
      const resource = source;
      tileset = await Cesium3DTileset.fromUrl(resource, {
        ...options.tilesetOptions,
      });
    }

    if (options.modelMatrix) {
      tileset.modelMatrix = options.modelMatrix;
    }

    viewer.scene.primitives.add(tileset);

    // Otomatik olarak kamera tileset'e odaklansın
    await viewer.zoomTo(tileset);

    return tileset;
  } catch (error) {
    console.error("3D Tiles yüklenirken hata oluştu:", error);
    return null;
  }
}

// Kullanım örnekleri:
// CesiumApi.add3DTileToViewer(viewer, "https://example.com/tileset.json");
// CesiumApi.add3DTileToViewer(viewer, 1452992); // Ion Asset ID
