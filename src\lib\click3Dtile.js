import {
  ScreenSpaceEventHandler,
  ScreenSpaceEventType,
  defined,
  Cesium3DTileFeature,
} from "cesium";

export function setupClickHandler(viewer) {
  const handler = new ScreenSpaceEventHandler(viewer.scene.canvas);

  handler.setInputAction((click) => {
    const picked = viewer.scene.pick(click.position);
    console.log("Picked object:", picked);

    if (!defined(picked)) {
      console.warn("Hiçbir şey seçilmedi.");
      return;
    }

    let feature = null;

    // Doğrudan feature mı?
    if (typeof picked.getProperty === "function") {
      feature = picked;
    }

    // Bazı durumlarda content.getFeature(index) ile alınabilir
    else if (
      defined(picked.content) &&
      typeof picked.content.getFeature === "function"
    ) {
      feature = picked.content.getFeature(0); // veya picked.instanceId
    }

    // Feature bulunduysa özellikleri yazdır
    if (feature && typeof feature.getPropertyNames === "function") {
      const props = feature.getPropertyNames();
      console.log("🎯 Seçilen feature'ın özellikleri:");
      props.forEach((key) => {
        console.log(`${key}: ${feature.getProperty(key)}`);
      });
    } else {
      console.warn("🚫 Seçilen nesne bir 3D Tiles feature değil.");
    }
  }, ScreenSpaceEventType.LEFT_CLICK);
}
