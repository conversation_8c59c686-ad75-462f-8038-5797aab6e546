import { Cartesian3, Color, LabelStyle, VerticalOrigin } from "cesium";

// Nokta ekleme fonksiyonu
export function addPointToViewer(viewer, lon, lat, label = "Harici Nokta") {
  const position = Cartesian3.fromDegrees(lon, lat);

  const entity = viewer.entities.add({
    position,
    point: {
      pixelSize: 10,
      color: Color.BLUE,
      outlineColor: Color.WHITE,
      outlineWidth: 2,
    },
    label: {
      text: label,
      font: "14pt sans-serif",
      style: LabelStyle.FILL_AND_OUTLINE,
      outlineWidth: 2,
      verticalOrigin: VerticalOrigin.TOP,
    },
  });

  // Sahneyi yeniden render etmek için çağrı
  viewer.scene.requestRender();

  return entity;
}
