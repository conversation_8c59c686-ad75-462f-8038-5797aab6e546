import { defineConfig } from "vite";
import { viteStaticCopy } from "vite-plugin-static-copy";
import react from "@vitejs/plugin-react";
import path from "path";

const cesiumSource = "node_modules/cesium/Build/Cesium";
// This is the base url for static files that CesiumJS needs to load.
// Set to an empty string to place the files at the site's root path
const cesiumBaseUrl = "cesiumStatic";

// https://vitejs.dev/config/
export default defineConfig({
  base: "./", // index.html içindeki tüm yolları ./assets/... olarak üret
  define: {
    CESIUM_BASE_URL: JSON.stringify("./cesiumStatic"),
    "process.env.NODE_ENV": JSON.stringify(
      process.env.NODE_ENV || "production"
    ),
    "process.env": JSON.stringify({}),
  },
  plugins: [
    react(),
    viteStaticCopy({
      targets: [
        { src: `${cesiumSource}/ThirdParty`, dest: cesiumBaseUrl },
        { src: `${cesiumSource}/Workers`, dest: cesiumBaseUrl },
        { src: `${cesiumSource}/Assets`, dest: cesiumBaseUrl },
        { src: `${cesiumSource}/Widgets`, dest: cesiumBaseUrl },
      ],
    }),
  ],
  build: {
    outDir: path.resolve(__dirname, "dist/lib"),
    lib: {
      entry: path.resolve(__dirname, "src/main.jsx"),
      name: "NCCesiumAPI",
      formats: ["iife"],
      fileName: () => "index.ncmapcesiumapi.js",
    },
    rollupOptions: {
      output: {
        chunkFileNames: "[name].[hash].js",
      },
    },
  },
  resolve: {
    alias: {
      cesium: path.resolve(__dirname, "node_modules/cesium"),
      "@": path.resolve(__dirname, "src"),
    },
  },
});
