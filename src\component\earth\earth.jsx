import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON>,
  Terrain,
  CesiumTerrainProvider,
} from "cesium";
import "cesium/Build/Cesium/Widgets/widgets.css";

Ion.defaultAccessToken =
  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************.H4k5QEiXbLCUvoPYg-6nf40QnKC3MjIerWvdKQxdves";

let viewer = null;
export default function Earth({ onViewerReady, options }) {
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    let isMounted = true;

    async function initViewer() {
      if (viewer === null && isMounted) {
        const terrainProvider = await CesiumTerrainProvider.fromIonAssetId(1); // ✅ Arazi modeli

        viewer = new Viewer("cesiumContainer", {
          terrainProvider,
          animation: false,
          baseLayerPicker: false,
          fullscreenButton: false,
          geocoder: false,
          homeButton: false,
          infoBox: false,
          sceneModePicker: false,
          selectionIndicator: false,
          timeline: false,
          navigationHelpButton: false,
          //requestRenderMode: true,
          // maximumRenderTimeChange: Infinity,
        });

        // Performans ayarları
        viewer.scene.globe.enableLighting = true;
        viewer.scene.globe.depthTestAgainstTerrain = true;
        viewer.scene.globe.showGroundAtmosphere = true;
        viewer.scene.debugShowFramesPerSecond = true;
        viewer.scene.fog.enabled = true;
        viewer.scene.highDynamicRange = true;

        // Kamera konumu
        viewer.camera.setView({
          destination: Cartesian3.fromDegrees(34.2433, 38.9833, 1350000),
          orientation: {
            heading: Math.toRadians(0.0),
            pitch: Math.toRadians(-90.0),
            roll: 0.0,
          },
        });

        setIsLoaded(true);
        if (onViewerReady) {
          onViewerReady(viewer);
        }
      }
    }

    initViewer();

    return () => {
      isMounted = false;
      if (viewer) {
        viewer.destroy();
        viewer = null;
      }
    };
  }, []);

  return (
    <div
      id="cesiumContainer"
      style={{
        width: "100%",
        height: "100vh",
        display: isLoaded ? "block" : "none",
      }}
    ></div>
  );
}
