let viewSheds = [];

export function addViewShed(viewer, options = {}) {
  if (!viewer || !window.Cesium?.ViewShed3D) {
    console.warn("Cesium.ViewShed3D tanımlı değil");
    return;
  }

  const shed = new window.Cesium.ViewShed3D(viewer, {
    horizontalAngle: options.horizontalAngle ?? 120,
    verticalAngle: options.verticalAngle ?? 90,
    distance: options.distance ?? 1000,
    calback: () => {
      if (typeof options.onDistanceChange === "function") {
        options.onDistanceChange(shed.distance);
      }
    },
  });

  viewSheds.push(shed);
  return shed;
}

export function clearViewSheds() {
  viewSheds.forEach((shed) => shed.destroy());
  viewSheds = [];
}
